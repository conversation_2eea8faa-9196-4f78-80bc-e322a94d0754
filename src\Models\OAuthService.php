<?php

namespace LBCDev\OAuthManager\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Encrypted;

class OAuthService extends Model
{
    use HasFactory;

    protected $provider;

    protected $table = 'oauth_services';

    protected $fillable = [
        'name',
        'slug',
        'service_type',
        'credentials',
        'access_token',
        'refresh_token',
        'expires_at',
        'is_active',
        'last_used_at',
    ];

    protected $casts = [
        'credentials' => 'array',
        // 'access_token' => Encrypted::class,
        // 'refresh_token' => Encrypted::class,
        'expires_at' => 'datetime',
        'last_used_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * Setter para inyectar un proveedor (útil para testing o personalización).
     */
    public function setProvider($provider)
    {
        $this->provider = $provider;
    }

    /**
     * Retorna la instancia del proveedor, o la crea si no existe.
     *
     */
    public function getProviderInstance()
    {
        if ($this->provider) {
            return $this->provider;
        }

        $providerClass = match ($this->service_type) {
            'google_drive' => \LBCDev\OAuthManager\Providers\GoogleDriveProvider::class,
            'onedrive' => \LBCDev\OAuthManager\Providers\OneDriveProvider::class,
            default => null,
        };

        if (!$providerClass) {
            throw new \InvalidArgumentException("Provider no definido para {$this->service_type}");
        }

        // Primero intentar obtener desde el contenedor (para mocks) 
        // try {
        //     $this->provider = app($providerClass);
        // } catch (\Exception $e) {
        //     // Si no existe en el contenedor, crear nueva instancia
        //     $this->provider = new $providerClass($this);
        // }


        // Primero intentar obtener desde el contenedor (para mocks) y si no existe en el contenedor, crear nueva instancia
        if (app()->bound($providerClass)) {
            $this->provider = app($providerClass)->setProvider($this);
        } else {
            $this->provider = new $providerClass($this);
        }

        return $this->provider;
    }

    public function ensureValidToken(): bool
    {
        if (!$this->access_token || $this->isTokenExpired()) {
            $provider = $this->getProviderInstance();

            $newTokenData = $provider->refreshToken();

            if ($newTokenData) {
                $this->update($newTokenData);
                return true;
            }

            return false; // Falló el refresco
        }

        return true; // Ya es válido
    }

    public function isTokenExpired(): bool
    {
        return $this->expires_at && now()->isAfter($this->expires_at);
    }

    public function needsRefresh(): bool
    {
        return $this->isTokenExpired() && $this->refresh_token;
    }

    /**
     * Revoca el token de acceso tanto en el proveedor como localmente.
     */
    public function revokeToken(): bool
    {
        if (!$this->access_token) {
            return false;
        }

        try {
            $provider = $this->getProviderInstance();

            // Intentar revocar en el proveedor remoto
            $revokedRemotely = $provider->revokeToken();

            // Limpiar tokens localmente independientemente del resultado remoto
            $this->update([
                'access_token' => null,
                'refresh_token' => null,
                'expires_at' => null,
                'last_used_at' => null,
            ]);

            return $revokedRemotely;
        } catch (\Exception $e) {
            // En caso de error, aún limpiar tokens localmente
            $this->update([
                'access_token' => null,
                'refresh_token' => null,
                'expires_at' => null,
                'last_used_at' => null,
            ]);

            return false;
        }
    }
}
